        String result = "";
        try {
            // 设置超时时间，并发送请求
            result = HttpRequest.post("http://10.6.5.224:8080/auth/waterPlatformLogin")
                    .header(Header.CONTENT_TYPE, "application/json;charset=utf-8")
                    .timeout(5000)  // 超时时间5秒
                    .body(JSON.toJSONString(loginMap))
                    .execute().body();
        } catch (Exception e) {
            // 记录异常日志或进行相应的容错处理
            System.err.println("请求失败: " + e.getMessage());
            // 可根据需求添加重试机制或返回默认值
            return ""; // 或者抛出自定义异常
        }

        if (StringUtils.isNotBlank(result)) {
            FeePlatformLoginBody feePlatformLoginBody = JSON.parseObject(result, FeePlatformLoginBody.class);
            if (feePlatformLoginBody.getCode() == HttpStatus.OK.value()) {
                feeToken = feePlatformLoginBody.getData().getAccessToken();
            }
        }
        return feeToken;
    }