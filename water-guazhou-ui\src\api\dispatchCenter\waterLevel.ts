import request from '@/plugins/axios'

// 清水池液位数据接口
export interface WaterLevelData {
  time: string
  value: number
  stationId?: string
  stationName?: string
  unit?: string
}

// 清水池液位查询参数
export interface WaterLevelQueryParams {
  stationId?: string
  stationType?: string
  projectId?: string
  startTime?: number
  endTime?: number
  queryType?: 'hour' | 'day' | 'month' | 'year'
  attributeId?: string
}

// 获取清水池液位变化数据
export function getWaterLevelTrendData(params: WaterLevelQueryParams) {
  return request({
    url: '/istar/api/waterLevel/getTrendData',
    method: 'get',
    params
  })
}

// 获取清水池实时液位数据
export function getRealtimeWaterLevel(params: {
  stationId?: string
  projectId?: string
  stationType?: string
}) {
  return request({
    url: '/istar/api/waterLevel/getRealtimeData',
    method: 'get',
    params
  })
}

// 获取清水池液位历史数据
export function getWaterLevelHistoryData(params: {
  stationId: string
  start: number
  end: number
  queryType: string
  attributeId?: string
}) {
  return request({
    url: '/istar/api/station/data/stationDayDataQuery',
    method: 'get',
    params
  })
}

// 获取清水池站点列表
export function getWaterPoolStationList(params: {
  projectId?: string
  name?: string
  stationType?: string
}) {
  return request({
    url: '/istar/api/station/data/detailList/view',
    method: 'get',
    params: {
      stationType: '清水池',
      ...params
    }
  })
}

// 获取清水池液位监测数据（24小时）
export function getWaterLevelDailyData(params: {
  stationId: string
  date?: string
}) {
  const today = new Date()
  const startTime = new Date(today.getFullYear(), today.getMonth(), today.getDate()).getTime()
  const endTime = startTime + 24 * 60 * 60 * 1000 - 1
  
  return request({
    url: '/istar/api/station/data/stationDayDataQuery',
    method: 'get',
    params: {
      stationId: params.stationId,
      start: startTime,
      end: endTime,
      queryType: 'hour',
      filterStart: 0,
      filterEnd: 23,
      attributeId: '清水池液位'
    }
  })
}

// 获取清水池液位统计数据
export function getWaterLevelStatistics(params: {
  stationId?: string
  projectId?: string
  startTime?: number
  endTime?: number
}) {
  return request({
    url: '/istar/api/waterLevel/getStatistics',
    method: 'get',
    params
  })
}
