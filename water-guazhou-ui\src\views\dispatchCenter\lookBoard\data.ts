import { padStart } from 'lodash-es'
import { hexToRgba } from '@/utils/GlobalHelper'

export const initLineChartOption = (yName?: string, data?: { beforeYesterday: Record<string, any>; today: Record<string, any>; yesterday: Record<string, any> }) => {
  const colors = ['#459f19', '#82a12f', '#3a89ac']
  const xData: string[] = Array.from({ length: 24 }).map((item, i) => padStart(i.toString(), 2, '0'))
  const Data1: number[] = []
  const Data2: number[] = []
  const Data3: number[] = []
  const beforeYesterday = data?.beforeYesterday || {}
  const today = data?.today || {}
  const yesterday = data?.yesterday || {}
  // Array.from({ length: 24 }).map((item, i) => {
  //   xData.push(i.toString())
  //   Data1.push(Number((Math.random() * basic * 0.2 + basic).toFixed(3)))
  //   Data2.push(Number((Math.random() * basic * 0.2 + basic).toFixed(3)))
  //   Data3.push(Number((Math.random() * basic * 0.2 + basic).toFixed(3)))
  // })
  for (const key in beforeYesterday || {}) {
    const cData = beforeYesterday[key]
    for (const cKey in cData) {
      Data1.push(cData[cKey])
    }
  }
  for (const key in yesterday || {}) {
    const cData = yesterday[key]
    for (const cKey in cData) {
      Data2.push(cData[cKey])
    }
  }
  for (const key in today || {}) {
    const cData = today[key]
    for (const cKey in cData) {
      Data3.push(cData[cKey])
    }
  }
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      show: true,
      right: 'center',
      top: 10,
      textStyle: {
        color: '#aaa'
      }
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: xData || [],
      axisLine: {
        lineStyle: {
          color: '#aaa'
        }
      },
      axisTick: {
        lineStyle: {
          color: '#aaa'
        }
      },
      axisLabel: {
        textStyle: {
          color: '#aaa'
        }
      }
    },
    yAxis: {
      type: 'value',
      name: yName,
      nameLoacation: 'top',
      splitLine: {
        lineStyle: {
          color: '#aaa',
          opacity: 0.2
        }
      },
      splitArea: {
        show: false
      },
      axisLine: {
        lineStyle: {
          color: '#aaa'
        }
      },
      axisTick: {
        lineStyle: {
          color: '#aaa'
        }
      },
      axisLabel: {
        textStyle: {
          color: '#aaa'
        }
      },
      boundaryGap: [0, '100%']
    },
    grid: {
      top: 40,
      left: 50,
      right: 20,
      bottom: 30
    },
    series: [
      {
        name: '前日',
        type: 'line',
        symbol: 'none',
        sampling: 'lttb',
        itemStyle: {
          color: colors[0]
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: hexToRgba(colors[0], 0.3) // 0% 处的颜色
              },
              {
                offset: 1,
                color: hexToRgba(colors[0], 0.1) // 100% 处的颜色
              }
            ],
            global: false // 缺省为 false
          }
        },
        data: Data1 || []
      },
      {
        name: '昨日',
        type: 'line',
        symbol: 'none',
        sampling: 'lttb',
        itemStyle: {
          color: colors[1]
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: hexToRgba(colors[1], 0.3) // 0% 处的颜色
              },
              {
                offset: 1,
                color: hexToRgba(colors[1], 0.1) // 100% 处的颜色
              }
            ],
            global: false // 缺省为 false
          }
        },
        data: Data2 || []
      },
      {
        name: '今日',
        type: 'line',
        symbol: 'none',
        sampling: 'lttb',
        itemStyle: {
          color: colors[2]
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: hexToRgba(colors[2], 0.3) // 0% 处的颜色
              },
              {
                offset: 1,
                color: hexToRgba(colors[2], 0.1) // 100% 处的颜色
              }
            ],
            global: false // 缺省为 false
          }
        },
        data: Data3 || []
      }
    ]
  }
  return option
}

// 清水池液位图表配置
export const initWaterLevelChartOption = (yName?: string, data?: Array<{time: string, value: number}>) => {
  const colors = ['#3a89ac'] // 使用与出水压力相同的颜色系列
  const xData: string[] = [];
  const yData: number[] = [];

  data?.forEach(item => {
    xData.push(item.time);
    yData.push(item.value);
  });

  const option = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      show: true,
      right: 'center',
      top: 10,
      textStyle: {
        color: '#aaa'
      }
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: xData || [],
      axisLine: {
        lineStyle: {
          color: '#aaa'
        }
      },
      axisTick: {
        lineStyle: {
          color: '#aaa'
        }
      },
      axisLabel: {
        textStyle: {
          color: '#aaa'
        }
      }
    },
    yAxis: {
      type: 'value',
      name: yName,
      nameLocation: 'top',
      splitLine: {
        lineStyle: {
          color: '#aaa',
          opacity: 0.2
        }
      },
      splitArea: {
        show: false
      },
      axisLine: {
        lineStyle: {
          color: '#aaa'
        }
      },
      axisTick: {
        lineStyle: {
          color: '#aaa'
        }
      },
      axisLabel: {
        textStyle: {
          color: '#aaa'
        }
      },
      boundaryGap: [0, '100%']
    },
    grid: {
      top: 40,
      left: 50,
      right: 20,
      bottom: 30
    },
    series: [
      {
        name: '清水池液位',
        type: 'line',
        symbol: 'none',
        sampling: 'lttb',
        itemStyle: {
          color: colors[0]
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: hexToRgba(colors[0], 0.3) // 0% 处的颜色
              },
              {
                offset: 1,
                color: hexToRgba(colors[0], 0.1) // 100% 处的颜色
              }
            ],
            global: false // 缺省为 false
          }
        },
        data: yData || []
      }
    ]
  };

  return option;
}
