<!-- 水厂管理-监测总览 -->
<template>
  <RightDrawerMap
    ref="refMap"
    title="监测总览"
    :detail-max-min="true"
    :hide-right-drawer="true"
    :hide-detail-close="true"
    @map-loaded="onMaploaded"
  >
    <template #detail-header>
      <span>监测总览</span>
    </template>
    <template #detail-default>
      <div class="detail-wrapper">
        <div class="wrapper">
          <SLCard class="wrapper-content hei" title="" overlay>
            <div class="statistics">
              <div
                v-for="(item, i) in state.Statistics"
                :key="i"
                class="statistics-item"
              >
                <div class="item-inner">
                  <Icon
                    :icon="
                      i === 0
                        ? 'material-symbols:water-dropdown-outline'
                        : i === 1
                          ? 'ep:calendar'
                          : i === 2
                            ? 'ep:takeaway-box'
                            : i === 3
                              ? 'ep:bell'
                              : ''
                    "
                    :size="30"
                  ></Icon>
                  <span class="item-text title">{{ item.title }}</span>
                  <div class="total" :class="item.className">
                    <span class="item-text count">{{ item.count }}</span>
                    <span class="item-text unit">{{ item.unit }}</span>
                  </div>
                </div>
              </div>
            </div>
            <div class="title_card">
              <el-tabs
                v-model="activeName"
                class="demo-tabs"
                @tab-change="handleClick"
              >
                <el-tab-pane
                  v-for="(item, i) in state.shuichangname"
                  :key="i"
                  :label="item.title"
                  :name="i"
                ></el-tab-pane>
              </el-tabs>
              <Search ref="refSearch" class="serch" :config="SearchConfig"></Search>
            </div>
            <div v-if="JSON.stringify(carditem) !== '{}'" class="card-box">
              <div class="card-item" :class="{ isDark: appStore.isDark }">
                <div class="card-content">
                  <SLCard title="监测数据" class="inner-card left" overlay>
                    <div class="monitor-box">
                      <div
                        v-for="(item, j) in carditem.monitorData"
                        :key="j"
                        class="monitor-item"
                        :class="{ isDark: appStore.isDark }"
                      >
                        <span
                          class="count"
                          :class="
                            item.status === true
                              ? 'text-green'
                              : item.status === false
                                ? 'text-red'
                                : 'text-blue'
                          "
                          >{{ (item.value * 1 || 0).toFixed(2) }}</span
                        >
                        <span class="total">
                          <span class="label">{{ item.propertyName }}</span>
                          <span v-if="item.unit" class="unit">{{ item.unit }}</span>
                        </span>
                      </div>
                    </div>
                  </SLCard>
                  <div class="inner-card">
                    <SLCard
                      class="chart-item"
                      :title="carditem.curMonitorData1.propertyName"
                    >
                      <VChart
                        :ref="'refChart' + carditem.id + '-1'"
                        :theme="appStore.isDark ? 'dark' : 'light'"
                        :option="carditem.curMonitorData1.lineChartOption"
                      >
                      </VChart>
                    </SLCard>
                    <SLCard
                      class="chart-item"
                      :title="carditem.curMonitorData2.propertyName"
                    >
                      <VChart
                        :ref="'refChart' + carditem.id + '-2'"
                        :theme="appStore.isDark ? 'dark' : 'light'"
                        :option="carditem.curMonitorData2.lineChartOption"
                      >
                      </VChart>
                    </SLCard>
                  </div>
                </div>
              </div>
            </div>

            <!-- 清水池液位变化折线图 -->
            <div class="water-level-chart">
              <SLCard title="清水池液位变化" class="chart-card" overlay>
                <VChart
                  ref="refWaterLevelChart"
                  :theme="appStore.isDark ? 'dark' : 'light'"
                  :option="waterLevelChartOption"
                >
                </VChart>
              </SLCard>
            </div>
          </SLCard>
        </div>
      </div>
    </template>
  </RightDrawerMap>
</template>
<script lang="ts" setup>
import { reactive } from 'vue';
import { Calendar, TakeawayBox, Bell } from '@element-plus/icons-vue';
import { IShuiChangData } from './type';
import { initLineChartOption, initWaterLevelChartOption } from './data';
import {
  GetStationDynamicRealtimeData,
  GetTreeDayStaionPressure,
  GetTreeDayStationFlow
} from '@/api/shuiwureports/bengzhan';
import { useAppStore, useBusinessStore } from '@/store';
import { GetLookBoardCountStatistic } from '@/api/shuiwureports/shuichangzonglan';
import { Icon } from '@iconify/vue';
import RightDrawerMap from '@/views/arcMap/components/common/RightDrawerMap.vue';
import { getWaterLevelDailyData, getWaterPoolStationList } from '@/api/dispatchCenter/waterLevel';

const activeName = ref(0);
const businessStore = useBusinessStore();
const appStore = useAppStore();
const refSearch = ref<ISearchIns>();
const refMap = ref<any>();
const refWaterLevelChart = ref<any>();
const { proxy }: any = getCurrentInstance();

// 地图相关状态
const staticState: {
  view?: __esri.MapView;
} = {};
const state = reactive<{
  Statistics: {
    title: string;
    count: number;
    unit: string;
    className: string;
  }[];
  shuichangname: any[];
  ShuiChangDatas: IShuiChangData[];
}>({
  Statistics: [
    { className: 'text-blue', title: '上月总供水量', count: 0, unit: 'm³' },
    { className: 'text-green', title: '本月总供水量', count: 0, unit: 'm³' },
    {
      className: 'text-orange',
      title: '年累计供水量',
      count: 0,
      unit: 'm³'
    },
    { className: 'text-purple', title: '报警数', count: 0, unit: '个' }
  ],
  shuichangname: [],
  ShuiChangDatas: []
});
const SearchConfig = reactive<ISearch>({
  filters: [
    {
      type: 'select-tree',
      label: '查看范围',
      field: 'projectId',
      checkStrictly: true,
      options: businessStore.projectList,
      nodeClick: (data) => {
        activeName.value = 0;
        businessStore.SET_selectedProject(data);
        refreshData(data);
      }
    }
  ],
  defaultParams: {
    projectId: businessStore.selectedProject?.value
  }
});
const refreshData = async (data?: NormalOption) => {
  GetLookBoardCountStatistic({
    stationType: '水厂',
    projectId: data?.value || businessStore.selectedProject?.value
  })
    .then((res) => {
      state.Statistics[0].count = (res.data?.lastMonthTotal || 0).toFixed(2);
      state.Statistics[1].count = (res.data?.monthTotal || 0).toFixed(2);
      state.Statistics[2].count = (res.data?.total || 0).toFixed(2);
      state.Statistics[3].count = (res.data?.alarm || 0).toFixed(0);
    })
    .catch(() => {
      state.Statistics[0].count = 0;
      state.Statistics[1].count = 0;
      state.Statistics[2].count = 0;
      state.Statistics[3].count = 0;
    });

  GetStationDynamicRealtimeData({
    projectId: data?.value,
    stationType: '水厂'
  }).then((res) => {
    state.shuichangname = [];
    res.data?.map((item) => {
      state.shuichangname.push({ title: item.name, stationData: item });
    });
    initData(state.shuichangname[0]?.stationData);
  });
};

async function initData(stationData?: Record<string, any>) {
  let pressure: any = {};
  let flow: any = {};
  try {
    pressure = await GetTreeDayStaionPressure({
      stationId: stationData?.stationId ?? ''
    });
    flow = await GetTreeDayStationFlow({
      stationId: stationData?.stationId ?? ''
    });
  } catch (error) {
    //
  }
  state.ShuiChangDatas = [];
  state.ShuiChangDatas.push({
    id: stationData?.stationId,
    title: stationData?.name,
    curMonitorData1: {
      value: 4011,
      unit: 'm³',
      propertyName: '出水流量(m³)',
      lineChartOption: initLineChartOption('出水流量(m³)', flow.data)
    },
    curMonitorData2: {
      value: 0.229,
      unit: 'Mpa',
      propertyName: '出水压力(Mpa)',
      lineChartOption: initLineChartOption('出水压力(Mpa)', pressure.data)
    },
    monitorData: stationData?.dataList || []
  });
  carditem.value = state.ShuiChangDatas[0];
  nextTick(() => {
    if (!stationData) return;
    const chart1 = proxy.$refs['refChart' + stationData.stationId + '-1'];
    chart1?.resize && chart1.resize();
    const chart2 = proxy.$refs['refChart' + stationData.stationId + '-2'];
    chart2?.resize && chart2.resize();
  });
}
onMounted(() => {
  refreshData();
});

const handleClick = (tab: any) => {
  carditem.value = state.ShuiChangDatas[tab];
  initData(state.shuichangname[tab].stationData);
};

const carditem = ref<any>({});

// 清水池液位图表选项
const waterLevelChartOption = ref<any>({});

// 地图加载完成事件
const onMaploaded = async (view: __esri.MapView) => {
  staticState.view = view;
  refMap.value?.toggleCustomDetail(true);
  await nextTick();
  refMap.value?.toggleCustomDetailMaxmin('max');
  // 初始化清水池液位数据
  await initWaterLevelChart();
};

// 初始化清水池液位图表
const initWaterLevelChart = async () => {
  try {
    // 获取清水池站点列表
    const stationList = await getWaterPoolStationList({
      projectId: businessStore.selectedProject?.value,
      stationType: '清水池'
    });

    let waterLevelData = [];

    if (stationList.data?.length > 0) {
      // 使用第一个清水池站点的数据
      const firstStation = stationList.data[0];
      const response = await getWaterLevelDailyData({
        stationId: firstStation.stationId || firstStation.id
      });

      // 处理API返回的数据
      if (response.data && Array.isArray(response.data)) {
        waterLevelData = response.data.map(item => ({
          time: item.ts || item.time,
          value: parseFloat(item.value || 0)
        }));
      }
    }

    // 如果没有真实数据，使用模拟数据
    if (waterLevelData.length === 0) {
      waterLevelData = generateMockWaterLevelData();
    }

    waterLevelChartOption.value = initWaterLevelChartOption('清水池液位(m)', waterLevelData);

    await nextTick();
    refWaterLevelChart.value?.resize && refWaterLevelChart.value.resize();
  } catch (error) {
    console.error('初始化清水池液位图表失败:', error);
    // 出错时使用模拟数据
    const mockData = generateMockWaterLevelData();
    waterLevelChartOption.value = initWaterLevelChartOption('清水池液位(m)', mockData);
  }
};

// 生成模拟清水池液位数据
const generateMockWaterLevelData = () => {
  const data = [];
  const now = new Date();

  for (let i = 23; i >= 0; i--) {
    const time = new Date(now.getTime() - i * 60 * 60 * 1000);
    const hour = time.getHours().toString().padStart(2, '0');
    const baseLevel = 3.5; // 基础液位3.5米
    const variation = Math.sin(i * 0.3) * 0.5 + Math.random() * 0.2 - 0.1; // 添加一些变化
    const level = Math.max(0, baseLevel + variation);

    data.push({
      time: hour + ':00',
      value: Number(level.toFixed(2))
    });
  }

  return data;
};
</script>
<style lang="scss" scoped>
.detail-wrapper {
  height: 100%;
  overflow-y: auto;
  padding: 0;
}

.wrapper {
  padding: 10px 0px 0px;
  height: 100%;
}

.wrapper-content {
  height: 100%;
  min-height: calc(100vh - 100px);
}

.title-header {
  margin-right: auto;
}

.statistics {
  display: flex;
  justify-content: space-around;
  padding: 0px 100px;
}

.statistics-item {
  padding: 8px;

  &:first-child {
    padding-left: 0;
  }

  &:last-child {
    padding-right: 0;
  }

  .item-inner {
    width: 100%;
    height: 100%;
    border-radius: 5px;
    padding: 10px;
    display: flex;
    align-items: center;
    font-size: 16px;

    .total {
      display: flex;
      align-items: baseline;
    }

    .item-text {
      margin: 0 8px;
    }

    .title {
      font-size: 16px;
    }

    .count {
      font-size: 22px;
    }

    .unit {
      font-size: 14px;
    }
  }
}

.text-blue {
  color: #399bff;
}

.text-green {
  color: #42c902;
}

.text-red {
  color: #af1313;
}

.text-orange {
  color: #318dff;
}

.text-purple {
  color: #ea556f;
}

:deep(.el-card__body) {
  padding: 12px;
}

.dark {
  .card-box {
    background-color: #191c27;
  }
}

.card-box {
  display: flex;
  width: 100%;
  height: calc(100% - 122px);
  margin-bottom: 15px;
  padding: 20px;
  background-color: var(--el-border-color);

  &:last-child {
    margin-bottom: 0;
  }

  .card-item {
    height: calc(100%);
    width: 100%;
    margin-bottom: 16px;

    &:last-child {
      margin-bottom: 0;
    }

    .card-title {
      display: flex;
      align-items: center;
    }

    .card-content {
      display: flex;
      height: 100%;
      justify-content: space-between;
    }

    .inner-card {
      width: calc(50% - 8px);
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      &.left {
        height: 100%;
      }
    }

    .chart-item {
      height: calc(50% - 10px);

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

.water-level-chart {
  margin-top: 20px;

  .chart-card {
    height: 350px;

    :deep(.sl-card-body) {
      height: calc(100% - 50px);
      padding: 10px;
    }

    :deep(.sl-card-header) {
      padding: 15px 20px;
      border-bottom: 1px solid #e8e8ed;

      .sl-card-title {
        font-size: 16px;
        font-weight: 500;
        color: #333;
      }
    }
  }
}

.monitor-box {
  display: flex;
  flex-wrap: wrap;

  .monitor-item {
    flex-basis: 25%;
    min-height: 80px;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    border-bottom: 1px solid #e8e8ed;
    background-color: #fff;

    // &:nth-child(1){
    //   background-color: #1a1d2d;
    // }
    &.isDark {
      border: 1px solid #222536;
      background-color: #1a1d2d;
    }

    .count {
      color: #42b8fe;
      font-size: 18px;
      line-height: 30px;
    }

    .total {
      display: flex;
      align-items: baseline;
    }

    .label {
      color: #949ab8;
      font-size: 14px;
    }

    .unit {
      font-size: 12px;
      margin-left: 8px;
    }
  }
}

.title_card {
  padding: 0px 20px;
  background-color: var(--el-bg-color-page);
  display: flex;
  align-items: center;
  height: 56px;
  justify-content: space-between;

  .serch {
    justify-content: flex-end;
  }
}
</style>

<style lang="scss">
.demo-tabs {
  .el-tabs__header {
    margin: 0px;
  }

  .el-tabs__nav {
    height: 56px;
    line-height: 56px;
  }

  .el-tabs__nav-wrap::after {
    height: 0px;
  }

  .el-tabs__item {
    height: 56px;
  }
}
</style>
