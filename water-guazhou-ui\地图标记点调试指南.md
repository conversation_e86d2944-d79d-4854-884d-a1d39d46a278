# 地图标记点调试指南

## 问题描述
地图上没有显示水厂位置标记点，需要进行调试和修复。

## 已实施的修复措施

### 1. 代码修复
- ✅ 添加了缺失的Vue导入（ref, onMounted, nextTick, getCurrentInstance, shallowRef）
- ✅ 简化了地图标记点配置，移除了复杂的自定义组件配置
- ✅ **移除了图标方法调用**：不再使用 `getStationImageUrl()` 方法
- ✅ **使用默认标记点样式**：移除了 `symbolConfig` 配置，使用地图默认样式
- ✅ 添加了详细的调试日志
- ✅ 增加了测试标记点，使用地图中心点附近的位置
- ✅ 添加了地图加载延迟，确保地图完全准备好
- ✅ 添加了手动刷新标记点的功能
- ✅ **立即添加简单测试标记点**：在地图加载完成后立即添加一个固定位置的标记点

### 2. 调试功能
- ✅ 在页面上添加了"刷新地图标记点"按钮
- ✅ 暴露了调试方法到全局window对象
- ✅ 添加了详细的控制台日志输出

## 调试步骤

### 步骤1: 检查控制台日志
1. 打开浏览器开发者工具（F12）
2. 切换到Console标签
3. 刷新页面，查看以下日志：
   - "地图加载完成:"
   - "开始初始化地图标记点..."
   - "当前地图视图:"
   - "地图中心点:"
   - "添加测试标记点:"
   - "最终生成的标记点数组:"

### 步骤2: 使用页面按钮调试
1. 在页面上找到"刷新地图标记点"按钮
2. 点击按钮，观察控制台输出
3. 检查是否有错误信息

### 步骤3: 使用浏览器控制台调试
在浏览器控制台中执行以下命令：

```javascript
// 检查地图视图状态
console.log('地图视图:', window.staticState);

// 检查标记点数组
console.log('标记点数组:', window.mapWindows);

// 手动刷新标记点
window.refreshMapMarkers();

// 检查地图中心点
if (window.staticState.view) {
  console.log('地图中心:', window.staticState.view.center);
  console.log('地图缩放级别:', window.staticState.view.zoom);
}
```

### 步骤4: 检查API数据
```javascript
// 检查水厂API数据
fetch('/api/mapservice/onemap/GetWaterPlantSupply?projectId=your-project-id')
  .then(res => res.json())
  .then(data => console.log('API数据:', data));
```

## 可能的问题和解决方案

### 问题1: 地图视图未正确初始化
**症状**: 控制台显示 "当前地图视图: undefined"
**解决方案**: 
- 检查RightDrawerMap组件是否正确加载
- 确认map-loaded事件是否正确触发

### 问题2: 标记点坐标计算错误
**症状**: 标记点数组为空或坐标为NaN
**解决方案**:
- 检查Point构造函数的参数
- 确认spatialReference是否正确设置

### 问题3: API数据格式不正确
**症状**: 控制台显示 "没有获取到水厂数据"
**解决方案**:
- 检查GetWaterPlantSupply API的返回格式
- 确认projectId参数是否正确

### 问题4: 图标文件路径错误
**症状**: 标记点显示但图标不显示
**解决方案**:
- 检查getStationImageUrl('水厂.png')返回的路径
- 确认图标文件是否存在

## 预期结果
修复后应该看到：
1. 控制台输出完整的调试信息
2. 地图上显示至少一个测试标记点
3. 如果有真实数据，显示真实的水厂标记点
4. 点击标记点能触发点击事件

## 下一步
如果标记点仍然不显示，请：
1. 提供控制台的完整日志输出
2. 检查网络请求是否成功
3. 确认地图组件的版本和配置
4. 考虑使用更简单的标记点配置进行测试
